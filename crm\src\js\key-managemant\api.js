import { crmUtils } from "crm";

/**
 * Key Management API Service
 * 钥匙管理相关的API接口
 */
export default {
    /**
     * 获取钥匙列表
     * @param {Object} params - 查询参数
     * @param {number} params.page - 页码，默认1
     * @param {number} params.limit - 每页数量，默认20，最大100
     * @param {string} params.status - 钥匙状态过滤：available, checked_out, lost, damaged, archived
     * @param {string} params.holder_id - 钥匙持有人id
     * @param {string} params.property_id - 根据propertyId查询keys,property详情页用
     * @param {string} params.property_type - 房产类型过滤：for_sale, to_let
     * @param {string} params.search - 搜索关键词（钥匙持有人名、房产地址）
     * @returns {Promise} API响应
     */
    getKeyList(params = {}) {
        // 设置默认参数
        const defaultParams = {
            page: 1,
            limit: 20
        };
        
        const queryParams = { ...defaultParams, ...params };
        
        return crmUtils.sendAjax({
            url: "/api/keys",
            type: "GET",
            data: queryParams
        }).then(response => {
            // 确保返回的数据结构符合预期
            if (response && response.data) {
                return {
                    success: response.status?.code === 200,
                    data: response.data.data || [],
                    total: response.data.total || 0,
                    page: queryParams.page,
                    limit: queryParams.limit
                };
            }
            return {
                success: false,
                data: [],
                total: 0,
                page: queryParams.page,
                limit: queryParams.limit
            };
        }).catch(error => {
            console.error('获取钥匙列表失败:', error);
            return {
                success: false,
                data: [],
                total: 0,
                page: queryParams.page,
                limit: queryParams.limit,
                error: error.message || '获取钥匙列表失败'
            };
        });
    },

    /**
     * 获取钥匙详情
     * @param {string} keyId - 钥匙ID
     * @returns {Promise} API响应
     */
    getKeyDetail(keyId) {
        return crmUtils.sendAjax({
            url: `/api/keys/${keyId}`,
            type: "GET"
        }).then(response => {
            if (response && response.data) {
                return {
                    success: response.status?.code === 200,
                    data: response.data || {}
                };
            }
            return {
                success: false,
                data: {}
            };
        }).catch(error => {
            console.error('获取钥匙详情失败:', error);
            return {
                success: false,
                data: {},
                error: error.message || '获取钥匙详情失败'
            };
        });
    },

    /**
     * 获取钥匙统计信息
     * @param {Object} params - 查询参数
     * @param {string} params.status - 钥匙状态过滤：available, checked_out, lost, damaged, archived
     * @param {string} params.holder_id - 钥匙持有人id
     * @param {string} params.property_id - property_id
     * @param {string} params.property_type - 房产类型过滤：for_sale, to_let
     * @param {string} params.search - 搜索关键词（钥匙持有人名、房产地址）
     * @returns {Promise} API响应
     */
    getKeyStatistics(params = {}) {
        return crmUtils.sendAjax({
            url: "/api/keys/statistics",
            type: "GET",
            data: params
        }).then(response => {
            if (response && response.data) {
                return {
                    success: response.status?.code === 200,
                    data: response.data || {}
                };
            }
            return {
                success: false,
                data: {}
            };
        }).catch(error => {
            console.error('获取钥匙统计信息失败:', error);
            return {
                success: false,
                data: {},
                error: error.message || '获取钥匙统计信息失败'
            };
        });
    },

    /**
     * 更新钥匙状态
     * @param {string} keyId - 钥匙ID
     * @param {Object} updateData - 更新数据
     * @returns {Promise} API响应
     */
    updateKeyStatus(keyId, updateData) {
        return crmUtils.sendAjax({
            url: `/api/keys/${keyId}`,
            type: "PUT",
            contentType: "application/json",
            data: JSON.stringify(updateData)
        }).then(response => {
            return {
                success: response.status?.code === 200,
                data: response.data || {}
            };
        }).catch(error => {
            console.error('更新钥匙状态失败:', error);
            return {
                success: false,
                error: error.message || '更新钥匙状态失败'
            };
        });
    }
};
