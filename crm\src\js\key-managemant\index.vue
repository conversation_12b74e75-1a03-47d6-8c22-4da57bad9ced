<template>
    <div class="key-management-page">
        <div class="page-header-section">Keys</div>
        <div class="key-management-wrapper">
            <div class="counts-section">
                <KeyStatistics :data="mockStatisticsData" />
            </div>

            <SearchFilter
                @search-change="handleSearchChange"
                @filter-change="handleFilterChange"
            />

            <!-- 加载状态 -->
            <div v-if="loading" class="loading-container">
                <div class="loading-spinner">加载中...</div>
            </div>

            <!-- 错误状态 -->
            <div v-else-if="error" class="error-container">
                <div class="error-message">{{ error }}</div>
                <button class="retry-button" @click="loadKeyList">重试</button>
            </div>

            <!-- 数据表格 -->
            <div v-else class="table-container">
                <KeyTable :data="keyList" />

                <!-- 分页组件 -->
                <div class="pagination-container" v-if="keyList.length > 0">
                    <Pagination
                        class="key-list-pagination"
                        @change="handlePaginationChange"
                        :activeIndex="pagination.page"
                        :pageSize="pagination.limit"
                        :totalPage="totalPage"
                        :pageSizeList="pageSizeList"
                        :showPageInput="true"
                        :showPageSize="true"
                        :showGuideArrow="true"
                        :rightPageNumber="1"
                    />
                </div>

                <!-- 空状态 -->
                <div v-if="keyList.length === 0 && !loading && !error" class="empty-container">
                    <div class="empty-message">暂无钥匙数据</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import SearchFilter from './components/SearchFilter.vue';
import KeyTable from './components/KeyTable.vue';
import KeyStatistics from './components/KeyStatistics.vue';
import { components } from 'common';
import keyApi from './api.js';

const { Pagination } = components;

export default {
    name: 'KeyManagement',
    components: {
        SearchFilter,
        KeyTable,
        KeyStatistics,
        Pagination
    },
    data() {
        return {
            // 搜索和过滤条件
            searchKeyword: '',
            filters: {
                status: 'all',
                holder_id: '',
                property_type: 'all'
            },

            // 分页相关
            pagination: {
                page: 1,
                limit: 20,
                total: 0
            },
            pageSizeList: [10, 20, 50, 100],

            // 数据相关
            keyList: [],
            loading: false,
            error: null,

            // 统计数据（暂时保留mock数据）
            mockStatisticsData: [],

            // 搜索防抖定时器
            searchTimer: null
        };
    },
    computed: {
        // 计算总页数
        totalPage() {
            return Math.ceil(this.pagination.total / this.pagination.limit);
        }
    },
    mounted() {
        // 页面加载时获取数据
        this.loadKeyList();
    },
    beforeDestroy() {
        // 清理搜索防抖定时器
        if (this.searchTimer) {
            clearTimeout(this.searchTimer);
        }
    },
    methods: {
        /**
         * 加载钥匙列表数据
         */
        async loadKeyList() {
            this.loading = true;
            this.error = null;

            try {
                // 构建API参数
                const params = {
                    page: this.pagination.page,
                    limit: this.pagination.limit
                };

                // 添加搜索关键词
                if (this.searchKeyword.trim()) {
                    params.search = this.searchKeyword.trim();
                }

                // 添加过滤条件
                if (this.filters.status !== 'all') {
                    params.status = this.filters.status;
                }
                if (this.filters.holder_id) {
                    params.holder_id = this.filters.holder_id;
                }
                if (this.filters.property_type !== 'all') {
                    params.property_type = this.filters.property_type;
                }

                // 调用API
                const response = await keyApi.getKeyList(params);

                if (response.success) {
                    this.keyList = response.data;
                    this.pagination.total = response.total;
                } else {
                    this.error = response.error || '获取数据失败';
                    this.keyList = [];
                    this.pagination.total = 0;
                }
            } catch (error) {
                console.error('加载钥匙列表失败:', error);
                this.error = '网络错误，请稍后重试';
                this.keyList = [];
                this.pagination.total = 0;
            } finally {
                this.loading = false;
            }
        },

        /**
         * 处理搜索变化（带防抖）
         */
        handleSearchChange(keyword) {
            this.searchKeyword = keyword;

            // 清除之前的定时器
            if (this.searchTimer) {
                clearTimeout(this.searchTimer);
            }

            // 设置防抖延迟
            this.searchTimer = setTimeout(() => {
                // 重置到第一页并重新加载数据
                this.pagination.page = 1;
                this.loadKeyList();
            }, 500); // 500ms防抖延迟
        },

        /**
         * 处理过滤条件变化
         */
        handleFilterChange(filters) {
            this.filters = { ...filters };
            // 重置到第一页并重新加载数据
            this.pagination.page = 1;
            this.loadKeyList();
        },

        /**
         * 处理分页变化
         */
        handlePaginationChange({ page, size }) {
            this.pagination.page = page;
            this.pagination.limit = size;
            this.loadKeyList();
        }
    }
};
</script>

<style scoped>
.key-management-page {
    background: #F6F7FB;
    min-height: 100vh;
    padding-bottom: 20px;
}

.page-header-section {
    font-family: Inter;
    font-size: 24px;
    font-weight: 700;
    line-height: 32px;
    color: #1A202C;
    background-color: #ffffff;
    border: 1px solid #EBECF1;
    height: 56px;
    line-height: 56px;
    padding-left: 20px;
}

.counts-section {
    display: flex;
}

.key-management-wrapper {
    width: 100%;
    max-width: 1440px;
    margin: 0 auto;
    padding: 0px 20px;
    background: #F6F7FB;
    min-height: 100vh;
    gap: 20px;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
}

.table-container {
    display: flex;
    flex-direction: column;
    gap: 0;
}

/* 加载状态样式 */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    background: #FFFFFF;
    border: 1px solid #EBECF1;
    border-radius: 6px;
}

.loading-spinner {
    font-family: 'SF Pro';
    font-size: 16px;
    color: #515666;
}

/* 错误状态样式 */
.error-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    background: #FFFFFF;
    border: 1px solid #EBECF1;
    border-radius: 6px;
    gap: 16px;
}

.error-message {
    font-family: 'SF Pro';
    font-size: 16px;
    color: #E53E3E;
}

.retry-button {
    padding: 8px 16px;
    background: #3182CE;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    font-family: 'SF Pro';
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.retry-button:hover {
    background: #2C5282;
}

/* 空状态样式 */
.empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    background: #FFFFFF;
    border: 1px solid #EBECF1;
    border-radius: 6px;
}

.empty-message {
    font-family: 'SF Pro';
    font-size: 16px;
    color: #A0A3AF;
}

/* 分页容器样式 */
.pagination-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 20px;
    background: #FFFFFF;
    border: 1px solid #EBECF1;
    border-top: none;
    border-radius: 0 0 6px 6px;
}

.key-list-pagination {
    margin: 0;
}
</style>
