<template>
    <div class="key-statistics" :class="{ 'collapsed': !isExpanded }">
        <div class="statistics-data">
            <div class="stat-item">
                <div class="stat-label">All<span v-if="!isExpanded">:</span></div>
                <div class="stat-value">{{ statistics.all }}</div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item">
                <div class="stat-label">Pending<span v-if="!isExpanded">:</span></div>
                <div class="stat-value">{{ statistics.pending }}</div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item">
                <div class="stat-label">Accepted<span v-if="!isExpanded">:</span></div>
                <div class="stat-value">{{ statistics.accepted }}</div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item">
                <div class="stat-label">Rejected<span v-if="!isExpanded">:</span></div>
                <div class="stat-value">{{ statistics.rejected }}</div>
            </div>
        </div>

        <div class="icon-area" @click="toggleExpanded">
            <i class="icon2017" :class="isExpanded ? 'icon-expand_up_02' : 'icon-expand_down_02'"></i>
        </div>
    </div>
</template>

<script>
export default {
    name: 'KeyStatistics',
    props: {
        data: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            isExpanded: true
        };
    },
    computed: {
        statistics() {
            const stats = {
                all: 0,
                pending: 0,
                accepted: 0,
                rejected: 0
            };

            if (!this.data || !this.data.length) {
                return {
                    all: 1200,
                    pending: 904,
                    accepted: 403,
                    rejected: 18
                };
            }

            stats.all = this.data.length;

            this.data.forEach(item => {
                switch (item.status) {
                    case 'available':
                        stats.pending++;
                        break;
                    case 'checked_out':
                        stats.accepted++;
                        break;
                    case 'archived':
                    case 'lost_damaged':
                        stats.rejected++;
                        break;
                }
            });

            return stats;
        }
    },
    methods: {
        toggleExpanded() {
            this.isExpanded = !this.isExpanded;
        }
    }
};
</script>

<style scoped>
.key-statistics {
    display: flex;
    gap: 20px;
    padding: 0 0 0 20px;
    width: 1400px;
    background: #FFFFFF;
    border: 1px solid #EBECF1;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.statistics-data {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 20px;
    padding: 20px 0;
    transition: padding 0.3s ease;
}

/* 折叠状态 */
.key-statistics.collapsed .statistics-data {
    padding: 15px 0;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
    transition: all 0.3s ease;
}

/* 折叠状态下的水平布局 */
.key-statistics.collapsed .stat-item {
    flex-direction: row;
    align-items: center;
    gap: 5px;
}

.stat-label {
    font-family: 'SF Pro Text';
    font-weight: 500;
    font-size: 14px;
    line-height: 1.43;
    color: #797E8B;
    transition: all 0.3s ease;
}

.stat-value {
    font-family: 'SF Pro Text';
    font-weight: 700;
    font-size: 22px;
    line-height: 1.36;
    color: #202437;
    transition: all 0.3s ease;
}

/* 折叠状态下的较小字体 */
.key-statistics.collapsed .stat-value {
    font-size: 14px;
    line-height: 1.43;
}

.stat-divider {
    width: 0;
    height: 100%;
    border-left: 1px solid #EBECF1;
    align-self: stretch;
}

.icon-area {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    background: rgba(93, 81, 226, 0.1);
    align-self: stretch;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.icon-area:hover {
    background: rgba(93, 81, 226, 0.15);
}

.icon-area .icon2017 {
    width: 10px;
    height: 10px;
    color: #5D51E2;
    font-size: 10px;
    transition: transform 0.3s ease;
}

/* 图标旋转动画 */
.key-statistics.collapsed .icon-area .icon2017 {
    transform: rotate(180deg);
}
</style>
