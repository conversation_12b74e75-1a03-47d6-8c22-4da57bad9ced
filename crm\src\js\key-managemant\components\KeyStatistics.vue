<template>
    <div class="key-statistics" :class="{ 'collapsed': !isExpanded }">
        <!-- 加载状态 -->
        <div v-if="loading" class="statistics-loading">
            <div class="loading-text">加载统计数据中...</div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="statistics-error">
            <div class="error-text">{{ error }}</div>
            <button class="retry-btn" @click="retryLoad">重试</button>
        </div>

        <!-- 统计数据 -->
        <div v-else class="statistics-data">
            <div class="stat-item">
                <div class="stat-label">Total<span v-if="!isExpanded">:</span></div>
                <div class="stat-value" :class="{ 'value-updated': valueUpdated }">
                    {{ formatNumber(statistics.total_keys || 0) }}
                </div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item">
                <div class="stat-label">Available<span v-if="!isExpanded">:</span></div>
                <div class="stat-value stat-available" :class="{ 'value-updated': valueUpdated }">
                    {{ formatNumber(statistics.available || 0) }}
                </div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item">
                <div class="stat-label">Checked Out<span v-if="!isExpanded">:</span></div>
                <div class="stat-value stat-checked-out" :class="{ 'value-updated': valueUpdated }">
                    {{ formatNumber(statistics.checked_out || 0) }}
                </div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item">
                <div class="stat-label">Lost<span v-if="!isExpanded">:</span></div>
                <div class="stat-value stat-lost" :class="{ 'value-updated': valueUpdated }">
                    {{ formatNumber(statistics.lost || 0) }}
                </div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item">
                <div class="stat-label">Damaged<span v-if="!isExpanded">:</span></div>
                <div class="stat-value stat-damaged" :class="{ 'value-updated': valueUpdated }">
                    {{ formatNumber(statistics.damaged || 0) }}
                </div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item">
                <div class="stat-label">Archived<span v-if="!isExpanded">:</span></div>
                <div class="stat-value stat-archived" :class="{ 'value-updated': valueUpdated }">
                    {{ formatNumber(statistics.archived || 0) }}
                </div>
            </div>
        </div>

        <div class="icon-area" @click="toggleExpanded">
            <i class="icon2017" :class="isExpanded ? 'icon-expand_up_02' : 'icon-expand_down_02'"></i>
        </div>
    </div>
</template>

<script>
import keyApi from '../api.js';

export default {
    name: 'KeyStatistics',
    props: {
        // 过滤参数，用于获取对应的统计数据
        filters: {
            type: Object,
            default: () => ({})
        },
        // 搜索关键词
        searchKeyword: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            isExpanded: true,
            statistics: {},
            loading: false,
            error: null,
            valueUpdated: false
        };
    },
    watch: {
        // 监听过滤条件变化，重新获取统计数据
        filters: {
            handler() {
                this.loadStatistics();
            },
            deep: true
        },
        // 监听搜索关键词变化
        searchKeyword() {
            this.loadStatistics();
        }
    },
    mounted() {
        // 组件挂载时加载统计数据
        this.loadStatistics();
    },
    methods: {
        /**
         * 加载统计数据
         */
        async loadStatistics() {
            this.loading = true;
            this.error = null;

            try {
                // 构建API参数
                const params = { ...this.filters };

                // 添加搜索关键词
                if (this.searchKeyword.trim()) {
                    params.search = this.searchKeyword.trim();
                }

                // 调用API
                const response = await keyApi.getKeyStatistics(params);

                if (response.success) {
                    // 检查数据是否有变化，触发更新动画
                    const hasChanged = JSON.stringify(this.statistics) !== JSON.stringify(response.data);
                    this.statistics = response.data;

                    if (hasChanged) {
                        this.triggerUpdateAnimation();
                    }
                } else {
                    this.error = response.error || '获取统计数据失败';
                    this.statistics = {};
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
                this.error = '网络错误，请稍后重试';
                this.statistics = {};
            } finally {
                this.loading = false;
            }
        },

        /**
         * 切换展开/折叠状态
         */
        toggleExpanded() {
            this.isExpanded = !this.isExpanded;
        },

        /**
         * 重试加载统计数据
         */
        retryLoad() {
            this.loadStatistics();
        },

        /**
         * 触发数值更新动画
         */
        triggerUpdateAnimation() {
            this.valueUpdated = true;
            setTimeout(() => {
                this.valueUpdated = false;
            }, 600);
        },

        /**
         * 格式化数字显示
         */
        formatNumber(num) {
            if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'k';
            }
            return num.toString();
        }
    }
};
</script>

<style scoped>
.key-statistics {
    display: flex;
    gap: 20px;
    padding: 0 0 0 20px;
    width: 100%;
    max-width: 1400px;
    background: #FFFFFF;
    border: 1px solid #EBECF1;
    border-radius: 6px;
    transition: all 0.3s ease;
    min-height: 80px;
}

/* 加载状态样式 */
.statistics-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    padding: 20px;
}

.loading-text {
    font-family: 'SF Pro Text';
    font-size: 14px;
    color: #797E8B;
}

/* 错误状态样式 */
.statistics-error {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    padding: 20px;
    gap: 12px;
}

.error-text {
    font-family: 'SF Pro Text';
    font-size: 14px;
    color: #F0454C;
}

.retry-btn {
    padding: 4px 8px;
    background: #3182CE;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    font-family: 'SF Pro Text';
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.retry-btn:hover {
    background: #2C5282;
}

.statistics-data {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 20px;
    padding: 20px 0;
    transition: padding 0.3s ease;
}

/* 折叠状态 */
.key-statistics.collapsed .statistics-data {
    padding: 15px 0;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
    transition: all 0.3s ease;
}

/* 折叠状态下的水平布局 */
.key-statistics.collapsed .stat-item {
    flex-direction: row;
    align-items: center;
    gap: 5px;
}

.stat-label {
    font-family: 'SF Pro Text';
    font-weight: 500;
    font-size: 14px;
    line-height: 1.43;
    color: #797E8B;
    transition: all 0.3s ease;
}

.stat-value {
    font-family: 'SF Pro Text';
    font-weight: 700;
    font-size: 22px;
    line-height: 1.36;
    color: #202437;
    transition: all 0.3s ease;
}

/* 数值更新动画 */
.stat-value.value-updated {
    animation: valueUpdate 0.6s ease-in-out;
}

@keyframes valueUpdate {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
        color: #5D51E2;
    }
    100% {
        transform: scale(1);
    }
}

/* 不同状态的颜色 */
.stat-available {
    color: #20C472;
}

.stat-checked-out {
    color: #5D51E2;
}

.stat-lost {
    color: #F0454C;
}

.stat-damaged {
    color: #F0454C;
}

.stat-archived {
    color: #C6C8D1;
}

/* 折叠状态下的较小字体 */
.key-statistics.collapsed .stat-value {
    font-size: 14px;
    line-height: 1.43;
}

.stat-divider {
    width: 0;
    height: 100%;
    border-left: 1px solid #EBECF1;
    align-self: stretch;
}

.icon-area {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    background: rgba(93, 81, 226, 0.1);
    align-self: stretch;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.icon-area:hover {
    background: rgba(93, 81, 226, 0.15);
}

.icon-area .icon2017 {
    width: 10px;
    height: 10px;
    color: #5D51E2;
    font-size: 10px;
    transition: transform 0.3s ease;
}

/* 图标旋转动画 */
.key-statistics.collapsed .icon-area .icon2017 {
    transform: rotate(180deg);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .key-statistics {
        padding: 0 0 0 15px;
    }

    .statistics-data {
        gap: 15px;
    }

    .stat-item {
        min-width: 80px;
    }

    .stat-value {
        font-size: 18px;
    }

    .key-statistics.collapsed .stat-value {
        font-size: 12px;
    }
}

@media (max-width: 768px) {
    .key-statistics {
        padding: 0 0 0 10px;
    }

    .statistics-data {
        gap: 10px;
        padding: 15px 0;
    }

    .stat-item {
        min-width: 60px;
    }

    .stat-label {
        font-size: 12px;
    }

    .stat-value {
        font-size: 16px;
    }

    .key-statistics.collapsed .stat-value {
        font-size: 11px;
    }
}
</style>
