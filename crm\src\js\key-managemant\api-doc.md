1. 获取钥匙列表
    **请求示例:**
    GET /api/keys?page=1&limit=20&status=available&property_type=for_sale&search=KEY-123
    | 参数名 | 类型 | 必填 | 说明 |
    |--------|------|------|------|
    | page | integer | 否 | 页码，默认1 |
    | limit | integer | 否 | 每页数量，默认20，最大100 |
    | status | string | 否 | 钥匙状态过滤：available, checked_out, lost, damaged, archived |
    | holder_id | string | 否 | 钥匙持有人id |
    | property_id | string | 否 | 根据propertyId查询keys,property详情页用 |
    | property_type | string | 否 | 房产类型过滤：for_sale, to_let |
    | search | string | 否 | 搜索关键词（钥匙持有人名、房产地址） |
    **响应结果**
    ```json
    {
    "success": true,
    "data": [
        {
        "id": "key-uuid-123",
        "key_code": "KEY-PROP123-001",
        "property_id": "prop-uuid-123",
        "property_address": "123 Main Street, London SW1A 1AA",
        "property_type": "for_sale",
        "description": "Front door key",
        "photo_url": "https://s3.amazonaws.com/keys/key-123.jpg",
        "status": "available",
        "storage_location": "Reception Desk",
        "current_holder_id": null,
        "current_holder_name": null,
        "current_holder_type": "internal",
        "total_keys": 1,
        "last_activity_at": "2025-07-17T14:30:00Z",
        "created_at": "2025-07-18T10:00:00Z",
        "updated_at": "2025-07-18T10:00:00Z"
        }
    ],
    "total": 45,
    }
    ```
2. 获取钥匙统计信息

`GET api/keys/statistics`

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | string | 否 | 钥匙状态过滤：available, checked_out, lost, damaged, archived |
| holder_id | string | 否 | 钥匙持有人id |
| property_id | string | 否 | property_id |
| property_type | string | 否 | 房产类型过滤：for_sale, to_let |
| search | string | 否 | 搜索关键词（钥匙持有人名、房产地址） |

**请求示例:**
```
GET /api/v1/keys/statistics?property_type=for_sale&date_range=month
```

**成功响应示例:**
```json
{
  "success": true,
  "data": {
    "total_keys": 156,
    "available": 89,
    "checked_out": 45,
    "lost": 8,
    "damaged": 3,
    "archived": 11
  }
}
```